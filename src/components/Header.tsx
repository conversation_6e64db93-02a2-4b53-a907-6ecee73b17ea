import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/contexts/ThemeContext";
import { Globe } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Header = () => {
  const { t, i18n } = useTranslation();
  const { setTheme, availableThemes } = useTheme();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <header className="fixed top-0 w-full bg-[#fcf9ee] backdrop-blur-sm z-50 border-b border-gray-100">
      <div className="container mx-auto px-6 flex items-center justify-between">
        <div className="flex items-center">
          <img src="/logo.png" alt="SÓNG Homestay" className="h-24 w-auto" />
        </div>

        <nav className="hidden md:flex items-center space-x-8">
          <a
            href="/"
            className="transition-colors hover:opacity-80"
            style={{ color: "var(--color-text)" }}
          >
            {t("nav.home")}
          </a>
          <a
            href="/about"
            className="transition-colors hover:opacity-80"
            style={{ color: "var(--color-text)" }}
          >
            {t("nav.about")}
          </a>
          <a
            href="/property"
            className="transition-colors hover:opacity-80"
            style={{ color: "var(--color-text)" }}
          >
            {t("nav.property")}
          </a>
          <a
            href="/contact"
            className="transition-colors hover:opacity-80"
            style={{ color: "var(--color-text)" }}
          >
            {t("nav.contact")}
          </a>
        </nav>

        <div className="flex items-center space-x-4">
          {/* Language Switcher */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <Globe className="h-4 w-4 mr-2" />
                {i18n.language.toUpperCase()}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => changeLanguage("vi")}>
                Tiếng Việt
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => changeLanguage("en")}>
                English
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => changeLanguage("ko")}>
                한국어
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Theme Switcher */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                Theme
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {Object.entries(availableThemes).map(([key, theme]) => (
                <DropdownMenuItem key={key} onClick={() => setTheme(key)}>
                  {theme.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            className="px-6 py-2 text-white hover:opacity-90"
            style={{ backgroundColor: "var(--color-primary)" }}
          >
            {t("nav.bookPackage")}
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
