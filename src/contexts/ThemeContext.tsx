import React, { createContext, useContext, useState, ReactNode } from "react";

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  textSecondary: string;
}

export interface ThemeConfig {
  name: string;
  colors: ThemeColors;
}

// Three main theme variations based on logo colors
export const themes: Record<string, ThemeConfig> = {
  ocean: {
    name: "Ocean",
    colors: {
      primary: "#0672af", // Bright sky blue (màu xanh da trời rực rỡ)
      secondary: "#105581", // Sky blue (màu xanh da trời)
      accent: "#E6F3FF", // Light blue accent
      background: "#FFFFFF",
      text: "#1E293B",
      textSecondary: "#64748B",
    },
  },
  earth: {
    name: "Earth",
    colors: {
      primary: "#059669", // Emerald green
      secondary: "#047857", // Darker green
      accent: "#ECFDF5", // Light green
      background: "#FFFFFF",
      text: "#1E293B",
      textSecondary: "#64748B",
    },
  },
  sunset: {
    name: "Sunset",
    colors: {
      primary: "#f3be1f", // Yellow (Màu vàng)
      secondary: "#0672af", // Bright sky blue as secondary
      accent: "#FFF9E6", // Light yellow accent
      background: "#FFFFFF",
      text: "#1E293B",
      textSecondary: "#64748B",
    },
  },
};

interface ThemeContextType {
  currentTheme: ThemeConfig;
  setTheme: (themeName: string) => void;
  availableThemes: Record<string, ThemeConfig>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(themes.ocean);

  const setTheme = (themeName: string) => {
    if (themes[themeName]) {
      setCurrentTheme(themes[themeName]);
      // Apply CSS custom properties
      const root = document.documentElement;
      const colors = themes[themeName].colors;

      root.style.setProperty("--color-primary", colors.primary);
      root.style.setProperty("--color-secondary", colors.secondary);
      root.style.setProperty("--color-accent", colors.accent);
      root.style.setProperty("--color-background", colors.background);
      root.style.setProperty("--color-text", colors.text);
      root.style.setProperty("--color-text-secondary", colors.textSecondary);
    }
  };

  // Initialize theme on mount
  React.useEffect(() => {
    setTheme("ocean");
  }, []);

  return (
    <ThemeContext.Provider
      value={{ currentTheme, setTheme, availableThemes: themes }}
    >
      {children}
    </ThemeContext.Provider>
  );
};
