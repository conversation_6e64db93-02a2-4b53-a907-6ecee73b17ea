<!DOCTYPE html>
<html lang="vi">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Primary Meta Tags -->
  <title>Sóng Homestay - Chữa lành từ không gian, chạm trong từng khoảnh khắc</title>
  <meta name="title" content="Sóng Homestay - Wellness Retreat Đà Nẵng" />
  <meta name="description"
    content="Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc tại Đà Nẵng. Trải nghiệm spa, văn hóa địa phương và hỗ trợ cảm xúc trong môi trường thiên nhiên tuyệt đẹp." />
  <meta name="keywords"
    content="homestay đà nẵng, wellness retreat, spa, chữa lành, nghỉ dưỡng, trải nghiệm văn hóa, meditation, yoga, sóng homestay" />
  <meta name="author" content="Sóng Homestay" />
  <meta name="robots" content="index, follow" />

  <!-- Language alternates -->
  <link rel="alternate" hreflang="vi" href="/" />
  <link rel="alternate" hreflang="en" href="/en" />
  <link rel="alternate" hreflang="ko" href="/ko" />
  <link rel="alternate" hreflang="x-default" href="/" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://songhomestay.com/" />
  <meta property="og:title" content="Sóng Homestay - Wellness Retreat Đà Nẵng" />
  <meta property="og:description"
    content="Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc. Trải nghiệm spa, văn hóa địa phương và hỗ trợ cảm xúc tại Đà Nẵng." />
  <meta property="og:image"
    content="https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:site_name" content="Sóng Homestay" />
  <meta property="og:locale" content="vi_VN" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://songhomestay.com/" />
  <meta property="twitter:title" content="Sóng Homestay - Wellness Retreat Đà Nẵng" />
  <meta property="twitter:description"
    content="Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc. Trải nghiệm spa, văn hóa địa phương và hỗ trợ cảm xúc tại Đà Nẵng." />
  <meta property="twitter:image"
    content="https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" />

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  <link rel="manifest" href="/manifest.json" />

  <!-- Theme Color -->
  <meta name="theme-color" content="#0EA5E9" />
  <meta name="msapplication-TileColor" content="#0EA5E9" />

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link rel="preconnect" href="https://images.unsplash.com" />

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LodgingBusiness",
    "name": "Sóng Homestay",
    "description": "Không gian nghỉ dưỡng mang yếu tố chữa lành và cảm xúc tại Đà Nẵng",
    "url": "https://songhomestay.com",
    "telephone": "+***********",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Đà Nẵng",
      "addressCountry": "VN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "16.0544",
      "longitude": "108.2022"
    },
    "amenityFeature": [
      {
        "@type": "LocationFeatureSpecification",
        "name": "Spa & Wellness Center"
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Private Garden"
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Free WiFi"
      },
      {
        "@type": "LocationFeatureSpecification",
        "name": "Beach Access"
      }
    ],
    "priceRange": "$$",
    "starRating": {
      "@type": "Rating",
      "ratingValue": "4.9"
    },
    "image": "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
  }
  </script>

  <!-- Performance optimizations -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com" />
  <link rel="dns-prefetch" href="//images.unsplash.com" />

  <!-- Preload critical resources -->
  <link rel="preload" href="/src/main.tsx" as="script" />
</head>

<body>
  <!-- Loading screen -->
  <div id="loading-screen" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
  ">
    <div style="
      color: white;
      font-family: 'Inter', sans-serif;
      text-align: center;
      animation: fadeInUp 1s ease-out;
    ">
      <h1 style="
        font-size: 3rem;
        font-weight: 300;
        margin: 0 0 1rem 0;
        letter-spacing: 0.1em;
      ">SÓNG</h1>
      <p style="
        font-size: 1.2rem;
        margin: 0 0 2rem 0;
        opacity: 0.9;
      ">Homestay</p>
      <div style="
        width: 50px;
        height: 3px;
        background: white;
        margin: 0 auto 2rem auto;
        animation: slideIn 1s ease-out 0.5s both;
      "></div>
      <p style="
        font-size: 1rem;
        font-style: italic;
        opacity: 0.8;
        animation: fadeIn 1s ease-out 1s both;
      ">"Chữa lành từ không gian – chạm trong từng khoảnh khắc"</p>
    </div>

    <!-- Loading spinner -->
    <div style="
      margin-top: 3rem;
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255,255,255,0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    "></div>
  </div>

  <!-- Main app -->
  <div id="root"></div>

  <!-- Scripts -->
  <script type="module" src="/src/main.tsx"></script>

  <!-- Hide loading screen when app loads -->
  <script>
    window.addEventListener('load', function () {
      setTimeout(function () {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.style.opacity = '0';
          setTimeout(function () {
            loadingScreen.style.display = 'none';
          }, 500);
        }
      }, 1500); // Show loading screen for at least 1.5 seconds
    });
  </script>

  <!-- CSS Animations -->
  <style>
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes slideIn {
      from {
        width: 0;
      }

      to {
        width: 50px;
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 0.8;
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* Prevent flash of unstyled content */
    #root {
      opacity: 0;
      transition: opacity 0.3s ease-in;
    }

    #root.loaded {
      opacity: 1;
    }
  </style>
</body>

</html>